import { config } from 'dotenv'
import { <PERSON><PERSON><PERSON>, PostgresDialect } from 'kysely'
import { Pool } from 'pg'
import { Database } from './models' // this is the Database interface we defined earlier

config({ path: __dirname + `/../../.env` })

const dialect = new PostgresDialect({
  pool: new Pool({
    database: process.env.DB_NAME,
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASS,
    port: process.env.DB_PORT,
    max: 10,
  }),
})

// Database interface is passed to <PERSON><PERSON><PERSON>'s constructor, and from now on, <PERSON><PERSON><PERSON>
// knows your database structure.
// Dialect is passed to <PERSON><PERSON><PERSON>'s constructor, and from now on, <PERSON><PERSON><PERSON> knows how
// to communicate with your database.
export const db = new Kysely<Database>({
  dialect,
})
