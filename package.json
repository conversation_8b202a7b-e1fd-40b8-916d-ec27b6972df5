{"name": "oauth2-auth-server", "version": "0.1.0", "main": "dist/index.js", "scripts": {"build": "npm run clean && tsc && copyfiles public/**/* dist && tsc-alias", "clean": "rimraf ./dist", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "dev": "npx tsc --noEmit && npx tsx --watch src/index.ts", "start": "node dist/index.js", "db:migrate": "npx kysely migrate:latest", "db:migrate:down": "npx kysely migrate:down", "db:seed": "npx tsx src/utils/seedData.ts", "db:inspect": "npx tsx src/utils/dbInspector.ts", "employee:update-password": "npx tsx src/utils/updatePassword.ts", "cli:reset-code": "npx tsx src/cli/generate-reset-code.ts", "cli:test-jsonb": "npx tsx src/cli/testJsonb.ts", "test:username-validation": "npx tsx src/utils/testUsernameValidation.ts", "test:api-username-validation": "npx tsx src/utils/testApiUsernameValidation.ts", "lint:fix": "npx eslint . --fix"}, "keywords": [], "author": "", "license": "ISC", "description": "OAuth 2.0 Authentication Server API", "devDependencies": {"@eslint/js": "^9.23.0", "@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.18", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^22.14.0", "@types/pg": "^8.11.10", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "@vitest/coverage-v8": "^3.1.4", "@vitest/ui": "^3.1.4", "copyfiles": "^2.4.1", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.4.0", "globals": "^16.0.0", "kysely-ctl": "^0.12.2", "prettier": "^3.5.3", "rimraf": "^6.0.1", "supertest": "^7.1.1", "tsc-alias": "^1.8.16", "tsx": "^4.19.3", "typescript": "^5.8.2", "typescript-eslint": "^8.29.0", "vitest": "^3.1.4"}, "dependencies": {"axios": "^1.9.0", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^5.1.0", "http-status-codes": "^2.3.0", "jsonwebtoken": "^9.0.2", "kysely": "^0.27.6", "mysql2": "^3.14.1", "node-fetch": "^3.3.2", "pg": "^8.16.0", "uuid": "^11.1.0", "zod": "^3.24.2"}}