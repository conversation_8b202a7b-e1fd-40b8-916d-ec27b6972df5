/**
 * Utility functions for handling JSONB columns in PostgreSQL
 * These functions help convert between TypeScript types and database-ready JSON strings
 */

import { 
  OAuthClientPreInsert, 
  OAuthClientPreUpdate, 
  NewOAuthClient, 
  OAuthClientUpdate 
} from '@models/oauth_clients'

/**
 * Converts OAuthClientPreInsert to NewOAuthClient by stringifying JSONB fields
 * @param preInsert - Data with JSONB fields as TypeScript arrays
 * @returns Database-ready insert data with stringified JSONB fields
 */
export function prepareOAuthClientForInsert(preInsert: OAuthClientPreInsert): Omit<NewOAuthClient, 'id'> {
  return {
    ...preInsert,
    redirect_uris: JSON.stringify(preInsert.redirect_uris),
    grant_types: JSON.stringify(preInsert.grant_types),
    webhook_events: preInsert.webhook_events ? JSON.stringify(preInsert.webhook_events) : null,
  }
}

/**
 * Converts OAuthClientPreUpdate to OAuthClientUpdate by stringifying JSONB fields
 * @param preUpdate - Data with JSONB fields as TypeScript arrays
 * @returns Database-ready update data with stringified JSONB fields
 */
export function prepareOAuthClientForUpdate(preUpdate: OAuthClientPreUpdate): Record<string, any> {
  const updateData: Record<string, any> = { ...preUpdate }

  // Stringify JSONB fields if they exist
  if (preUpdate.redirect_uris) {
    updateData.redirect_uris = JSON.stringify(preUpdate.redirect_uris)
  }

  if (preUpdate.grant_types) {
    updateData.grant_types = JSON.stringify(preUpdate.grant_types)
  }

  if (preUpdate.webhook_events !== undefined) {
    updateData.webhook_events = preUpdate.webhook_events ? JSON.stringify(preUpdate.webhook_events) : null
  }

  return updateData
}

/**
 * Generic helper to stringify JSONB fields in any object
 * @param data - Object that may contain JSONB fields
 * @param jsonbFields - Array of field names that should be stringified
 * @returns Object with specified fields stringified
 */
export function stringifyJsonbFields<T extends Record<string, any>>(
  data: T,
  jsonbFields: (keyof T)[]
): T {
  const result = { ...data }

  for (const field of jsonbFields) {
    if (result[field] !== undefined && result[field] !== null) {
      if (Array.isArray(result[field]) || typeof result[field] === 'object') {
        result[field] = JSON.stringify(result[field])
      }
    }
  }

  return result
}

/**
 * Type guard to check if a value needs JSON stringification
 * @param value - Value to check
 * @returns True if value should be stringified for JSONB storage
 */
export function needsStringification(value: any): boolean {
  return value !== null && 
         value !== undefined && 
         (Array.isArray(value) || (typeof value === 'object' && typeof value !== 'string'))
}

/**
 * Safely stringify a value for JSONB storage
 * @param value - Value to stringify
 * @returns JSON string or null
 */
export function safeJsonStringify(value: any): string | null {
  if (value === null || value === undefined) {
    return null
  }
  
  if (typeof value === 'string') {
    // If it's already a string, check if it's valid JSON
    try {
      JSON.parse(value)
      return value // Already valid JSON string
    } catch {
      // Not valid JSON, stringify it
      return JSON.stringify(value)
    }
  }
  
  return JSON.stringify(value)
}
