import * as employeeController from '@controllers/employeeController'
import { Employee } from '@models/employees'
import { cleanupTestEmployees, createTestEmployee } from '@test/setup'
import express from 'express'
import request from 'supertest'
import { afterEach, beforeEach, describe, expect, it } from 'vitest'

// Create test app
const app = express()
app.use(express.json())

// Add routes
app.post('/api/v1/employees/setup-oauth', employeeController.setupOAuthAccess)
app.put('/api/v1/employees/:id', employeeController.updateEmployee)
app.get('/api/v1/employees/:id', employeeController.getEmployeeById)
app.delete('/api/v1/employees/:id', employeeController.removeOAuthAccess)
app.post(
  '/api/v1/employees/:id/change-password',
  employeeController.changePassword
)

describe('EmployeeController', () => {
  let testEmployee: Employee

  beforeEach(async () => {
    testEmployee = await createTestEmployee({
      first_name: 'API',
      last_name: 'Employee',
      email: '<EMAIL>',
    })
  })

  afterEach(async () => {
    await cleanupTestEmployees()
  })

  describe('POST /api/v1/employees/setup-oauth', () => {
    it('should setup OAuth access with valid data', async () => {
      const response = await request(app)
        .post('/api/v1/employees/setup-oauth')
        .send({
          employee_id: testEmployee.employee_id,
          username: 'apiemployee123',
          password: 'testPassword123',
        })

      expect(response.status).toBe(201)
      expect(response.body).toHaveProperty(
        'message',
        'OAuth access configured successfully'
      )
      expect(response.body.employee).toHaveProperty(
        'employee_id',
        testEmployee.employee_id
      )
      expect(response.body.employee).toHaveProperty(
        'username',
        'apiemployee123'
      )
    })

    it('should reject OAuth setup with invalid username containing @', async () => {
      const response = await request(app)
        .post('/api/v1/employees/setup-oauth')
        .send({
          employee_id: testEmployee.employee_id,
          username: '<EMAIL>',
          password: 'testPassword123',
        })

      expect(response.status).toBe(400)
      expect(response.body).toHaveProperty('error', 'invalid_request')
      expect(response.body.message).toContain(
        'Username cannot contain @ character'
      )
    })

    it('should reject OAuth setup with username containing only numbers', async () => {
      const response = await request(app)
        .post('/api/v1/employees/setup-oauth')
        .send({
          employee_id: testEmployee.employee_id,
          username: '12345',
          password: 'testPassword123',
        })

      expect(response.status).toBe(400)
      expect(response.body).toHaveProperty('error', 'invalid_request')
      expect(response.body.message).toContain(
        'Username must contain at least 1 alphabetic character'
      )
    })

    it('should setup OAuth access without username', async () => {
      const response = await request(app)
        .post('/api/v1/employees/setup-oauth')
        .send({
          employee_id: testEmployee.employee_id,
          password: 'testPassword123',
        })

      expect(response.status).toBe(201)
      expect(response.body).toHaveProperty(
        'message',
        'OAuth access configured successfully'
      )
      expect(response.body.employee).toHaveProperty(
        'employee_id',
        testEmployee.employee_id
      )
    })

    it('should reject OAuth setup for non-existent employee', async () => {
      const response = await request(app)
        .post('/api/v1/employees/setup-oauth')
        .send({
          employee_id: 99999,
          username: 'validuser',
          password: 'testPassword123',
        })

      expect(response.status).toBe(404)
      expect(response.body).toHaveProperty('error', 'not_found')
      expect(response.body.message).toContain(
        'Employee must be created by HR system first'
      )
    })
  })

  describe('PUT /api/v1/employees/:id', () => {
    beforeEach(async () => {
      // Set up initial OAuth access
      await request(app).post('/api/v1/employees/setup-oauth').send({
        employee_id: testEmployee.employee_id,
        username: 'initialemployee',
        password: 'initialPassword123',
      })
    })

    it('should update username with valid data', async () => {
      const response = await request(app)
        .put(`/api/v1/employees/${testEmployee.employee_id}`)
        .send({
          username: 'updatedemployee123',
        })

      expect(response.status).toBe(200)
      expect(response.body).toHaveProperty('username', 'updatedemployee123')
    })

    it('should reject username update with invalid username', async () => {
      const response = await request(app)
        .put(`/api/v1/employees/${testEmployee.employee_id}`)
        .send({
          username: '<EMAIL>',
        })

      expect(response.status).toBe(400)
      expect(response.body).toHaveProperty('error', 'invalid_request')
      expect(response.body.message).toContain(
        'Username cannot contain @ character'
      )
    })

    it('should update password', async () => {
      const response = await request(app)
        .put(`/api/v1/employees/${testEmployee.employee_id}`)
        .send({
          password: 'newPassword456',
        })

      expect(response.status).toBe(200)
    })

    it('should reject update for non-existent employee', async () => {
      const response = await request(app).put('/api/v1/employees/99999').send({
        username: 'validuser',
      })

      expect(response.status).toBe(404)
      expect(response.body).toHaveProperty('error', 'not_found')
      expect(response.body.message).toContain('Employee not found')
    })
  })

  describe('GET /api/v1/employees/:id', () => {
    beforeEach(async () => {
      // Set up initial OAuth access
      await request(app).post('/api/v1/employees/setup-oauth').send({
        employee_id: testEmployee.employee_id,
        username: 'getemployee',
        password: 'getPassword123',
      })
    })

    it('should get employee by ID', async () => {
      const response = await request(app).get(
        `/api/v1/employees/${testEmployee.employee_id}`
      )

      expect(response.status).toBe(200)
      expect(response.body).toHaveProperty(
        'employee_id',
        testEmployee.employee_id
      )
      expect(response.body).toHaveProperty('username', 'getemployee')
      expect(response.body).toHaveProperty('first_name', 'API')
      expect(response.body).toHaveProperty('last_name', 'Employee')
    })

    it('should return 404 for non-existent employee', async () => {
      const response = await request(app).get('/api/v1/employees/99999')

      expect(response.status).toBe(404)
      expect(response.body).toHaveProperty('error', 'not_found')
    })

    it('should reject invalid employee ID', async () => {
      const response = await request(app).get('/api/v1/employees/invalid')

      expect(response.status).toBe(400)
      expect(response.body).toHaveProperty('error', 'invalid_request')
    })
  })
})
