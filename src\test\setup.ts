import { db } from '@db/database'
import { NewEmployee } from '@models/employees'
import { NewOAuthClient } from '@models/oauth_clients'
import dotenv from 'dotenv'
import { afterAll, afterEach, beforeAll, beforeEach } from 'vitest'

const timestamp = Date.now()
const random = Math.floor(Math.random() * 1000)

const TEST_EMPLOYEE_ID = 7654321
const TEST_CLIENT_ID = `test-client-${timestamp}-${random}`

// Load environment variables for testing
dotenv.config()

// Global test setup
beforeAll(async () => {
  console.log('🧪 Setting up test environment...')

  // Ensure we're using a test database
  if (!process.env.DATABASE_URL?.includes('test')) {
    console.log(
      '⚠️  Warning: Not using a test database. Consider setting DATABASE_URL to a test database.'
    )
  }
})

// Global test teardown
afterAll(async () => {
  console.log('🧹 Cleaning up test environment...')

  try {
    // Close database connections
    await db.destroy()
  } catch (error) {
    console.error('Error during test cleanup:', error)
  }
})

// Setup before each test
beforeEach(async () => {
  // Any per-test setup can go here
})

// Cleanup after each test
afterEach(async () => {
  // Any per-test cleanup can go here
})

// Helper function to create test employee
export async function createTestEmployee(
  overrides: Partial<Omit<NewEmployee, 'employee_id'>> = {}
) {
  // Generate a unique identifier for test employees
  const timestamp = Date.now()
  const random = Math.floor(Math.random() * 1000)

  const defaultEmployee: NewEmployee = {
    employee_id: TEST_EMPLOYEE_ID,
    status: 1,
    first_name: 'Test',
    last_name: 'User',
    employment_date: new Date().toISOString().split('T')[0],
    email: `test.user.${timestamp}.${random}@test.com`, // Ensure unique email
    ...overrides,
  }

  // MySQL doesn't support RETURNING, so we need to insert and then get the ID
  const result = await db
    .insertInto('auth.employees')
    .values(defaultEmployee)
    .execute()

  const employee = await db
    .selectFrom('auth.employees')
    .selectAll()
    .where('employee_id', '=', defaultEmployee.employee_id)
    .executeTakeFirst()

  if (!employee) {
    throw new Error('Failed to create test employee')
  }

  return employee
}

// Helper function to cleanup test employees
export async function cleanupTestEmployees() {
  await db
    .deleteFrom('auth.employees')
    .where('employee_id', '=', TEST_EMPLOYEE_ID)
    .execute()
}

// Helper function to create test OAuth client
export async function createTestOAuthClient(
  overrides: Partial<NewOAuthClient> = {}
) {
  // Generate unique identifiers for test clients
  const timestamp = Date.now()
  const random = Math.floor(Math.random() * 1000)

  const defaultClient = {
    client_name: 'Test Client',
    client_id: TEST_CLIENT_ID,
    client_secret: `test-secret-${timestamp}-${random}`,
    redirect_uris: JSON.stringify(
      overrides.redirect_uris || ['http://localhost:3000/callback']
    ),
    grant_types: JSON.stringify(
      overrides.grant_types || ['authorization_code', 'refresh_token']
    ),
    scope: overrides.scope || 'read write',
    is_active: overrides.is_active ?? true,
    webhook_url: overrides.webhook_url || null,
    webhook_secret: overrides.webhook_secret || null,
    webhook_events: overrides.webhook_events
      ? JSON.stringify(overrides.webhook_events)
      : null,
  }

  const result = await db
    .insertInto('auth.oauth_clients')
    .values(defaultClient)
    .execute()

  const client = await db
    .selectFrom('auth.oauth_clients')
    .selectAll()
    .where('client_id', '=', defaultClient.client_id)
    .executeTakeFirst()

  if (!client) {
    throw new Error('Failed to create test OAuth client')
  }

  // Parse JSONB columns for return
  return {
    ...client,
    redirect_uris:
      typeof client.redirect_uris === 'string'
        ? JSON.parse(client.redirect_uris)
        : client.redirect_uris,
    grant_types:
      typeof client.grant_types === 'string'
        ? JSON.parse(client.grant_types)
        : client.grant_types,
    webhook_events: client.webhook_events
      ? typeof client.webhook_events === 'string'
        ? JSON.parse(client.webhook_events)
        : client.webhook_events
      : null,
  }
}

// Helper function to cleanup test OAuth clients
export async function cleanupTestOAuthClients() {
  await db
    .deleteFrom('auth.oauth_clients')
    .where('client_id', '=', TEST_CLIENT_ID)
    .execute()
}
