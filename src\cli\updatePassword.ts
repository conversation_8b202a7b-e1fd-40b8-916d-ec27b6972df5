import { db } from '../db/database'
import { employeeService } from '../services/employeeService'

/**
 * CLI utility to update an employee's password by employee_id
 */
export async function updateEmployeePassword() {
  try {
    // Get command line arguments
    const args = process.argv.slice(2)
    const employeeId = args[0]
    const newPassword = args[1]

    // Validate arguments
    if (!employeeId || !newPassword) {
      console.log('❌ Error: Missing required arguments')
      console.log('')
      console.log(
        'Usage: npm run employee:update-password <employee_id> <new_password>'
      )
      console.log('')
      console.log('Examples:')
      console.log(
        '  npm run employee:update-password 123 "newSecurePassword123"'
      )
      console.log('  npm run employee:update-password 456 "anotherPassword456"')
      console.log('')
      console.log(
        'Note: Wrap passwords in quotes if they contain special characters'
      )
      return
    }

    // Validate employee_id is a number
    const employeeIdNum = parseInt(employeeId, 10)
    if (isNaN(employeeIdNum) || employeeIdNum <= 0) {
      console.log('❌ Error: employee_id must be a positive number')
      console.log(`   Provided: "${employeeId}"`)
      return
    }

    // Validate password/PIN
    const isPin = /^\d{4,8}$/.test(newPassword)

    if (isPin) {
      // Validate PIN (4-8 digits)
      if (newPassword.length < 4 || newPassword.length > 8) {
        console.log('❌ Error: PIN must be 4-8 digits long')
        console.log(`   Provided PIN length: ${newPassword.length}`)
        return
      }
      console.log('🔢 Detected PIN format (4-8 numeric digits)')
    } else {
      // Validate password (must have at least 1 alphabetic character and be at least 6 chars)
      if (newPassword.length < 6) {
        console.log('❌ Error: Password must be at least 6 characters long')
        console.log(`   Provided password length: ${newPassword.length}`)
        return
      }

      const hasAlphaChar = /[a-zA-Z]/.test(newPassword)
      if (!hasAlphaChar) {
        console.log(
          '❌ Error: Password must contain at least 1 alphabetic character'
        )
        console.log('   If you want to set a numeric PIN, use 4-8 digits only')
        return
      }
      console.log(
        '🔐 Detected password format (contains alphabetic characters)'
      )
    }

    console.log('🔍 Looking up employee...')

    // Check if employee exists
    const employee = await employeeService.getEmployeeById(employeeIdNum)
    if (!employee) {
      console.log(
        `❌ Error: Employee with ID ${employeeIdNum} not found or inactive`
      )
      return
    }

    console.log(
      `✅ Found employee: ${employee.first_name} ${employee.last_name}`
    )
    console.log(`   Username: ${employee.username || 'Not set'}`)
    console.log(`   Email: ${employee.email || 'Not set'}`)
    console.log('')
    console.log(`🔐 Updating ${isPin ? 'PIN' : 'password'}...`)

    // Update the password/PIN
    const success = await employeeService.updateEmployeePassword(
      employeeIdNum,
      newPassword
    )

    if (success) {
      console.log(`✅ ${isPin ? 'PIN' : 'Password'} updated successfully!`)
      console.log(`   Employee ID: ${employeeIdNum}`)
      console.log(`   Employee: ${employee.first_name} ${employee.last_name}`)
      console.log('')
      console.log(
        `💡 The employee can now log in with their new ${isPin ? 'PIN' : 'password'}`
      )
    } else {
      console.log(`❌ Failed to update ${isPin ? 'PIN' : 'password'}`)
      console.log(
        '   This could be due to a database error or the employee not existing'
      )
    }
  } catch (error) {
    console.error('💥 Failed to update employee password:', error)
    if (error instanceof Error) {
      console.error('   Error details:', error.message)
    }
  } finally {
    // Close the database connection
    try {
      await db.destroy()
    } catch (error) {
      console.error('Warning: Failed to close database connection:', error)
    }
    process.exit(0)
  }
}

// If this file is run directly, execute the password update
if (require.main === module) {
  updateEmployeePassword()
}
