import {
  ColumnType,
  Generated,
  Insertable,
  JSONColumnType,
  Selectable,
  Updateable,
} from 'kysely'

export interface OAuthClientsTable {
  id: Generated<number>
  client_id: string
  client_secret: string
  client_name: string
  redirect_uris: JSONColumnType<string[]>
  grant_types: JSONColumnType<string[]>
  scope: string // space-separated scopes
  is_active: boolean
  webhook_url: string | null // URL to send webhook notifications
  webhook_secret: string | null // Secret for webhook signature verification
  webhook_events: JSONColumnType<string[]> | null // JSON array of events to subscribe to
  created_at: ColumnType<Date, string | undefined, never>
  updated_at: ColumnType<Date, string | undefined, string | undefined>
}

// Export types for OAuth Clients
export type OAuthClient = Selectable<OAuthClientsTable>
export type NewOAuthClient = Insertable<OAuthClientsTable>
export type OAuthClientUpdate = Updateable<OAuthClientsTable>

// Pre-insert type: Allows JSONB fields as their actual TypeScript types before stringification
export interface OAuthClientPreInsert {
  client_id?: string
  client_secret?: string
  client_name: string
  redirect_uris: string[] // Array instead of JSONColumnType
  grant_types: string[] // Array instead of JSONColumnType
  scope: string
  is_active?: boolean
  webhook_url?: string | null
  webhook_secret?: string | null
  webhook_events?: string[] | null // Array instead of JSONColumnType
  created_at?: string
  updated_at?: string
}

// Pre-update type: Allows JSONB fields as their actual TypeScript types before stringification
export interface OAuthClientPreUpdate {
  client_id?: string
  client_secret?: string
  client_name?: string
  redirect_uris?: string[] // Array instead of JSONColumnType
  grant_types?: string[] // Array instead of JSONColumnType
  scope?: string
  is_active?: boolean
  webhook_url?: string | null
  webhook_secret?: string | null
  webhook_events?: string[] | null // Array instead of JSONColumnType
  created_at?: string
  updated_at?: string
}
