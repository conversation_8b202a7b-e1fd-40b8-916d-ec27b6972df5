/**
 * Example demonstrating the usage of pre-insert and pre-update types for JSONB columns
 * This shows how the new types improve maintainability and type safety
 */

import { oauthClientService } from '@services/oauthClientService'
import { OAuthClientPreInsert, OAuthClientPreUpdate } from '@models/oauth_clients'

// Example: Creating a new OAuth client with proper TypeScript types
async function createExampleClient() {
  console.log('🔧 Creating OAuth client with typed JSONB fields...')

  // ✅ BEFORE: We can now use proper TypeScript arrays instead of strings
  const clientData: OAuthClientPreInsert = {
    client_name: 'Example App',
    redirect_uris: [
      'http://localhost:3000/callback',
      'https://example.com/oauth/callback'
    ], // ✅ TypeScript array - no manual JSON.stringify needed!
    grant_types: [
      'authorization_code',
      'refresh_token'
    ], // ✅ TypeScript array - type-safe!
    scope: 'read write',
    webhook_url: 'https://example.com/webhooks/oauth',
    webhook_events: [
      'token.created',
      'token.revoked',
      'client.updated'
    ], // ✅ TypeScript array - intellisense works!
    is_active: true
  }

  try {
    const client = await oauthClientService.createClient(clientData)
    console.log('✅ Client created:', {
      id: client.id,
      name: client.client_name,
      redirect_uris: client.redirect_uris, // Returns as parsed array
      grant_types: client.grant_types, // Returns as parsed array
      webhook_events: client.webhook_events // Returns as parsed array
    })
    return client
  } catch (error) {
    console.error('❌ Failed to create client:', error)
    throw error
  }
}

// Example: Updating an OAuth client with proper TypeScript types
async function updateExampleClient(clientId: number) {
  console.log('🔧 Updating OAuth client with typed JSONB fields...')

  // ✅ BEFORE: We can now use proper TypeScript arrays for updates too
  const updates: OAuthClientPreUpdate = {
    redirect_uris: [
      'http://localhost:3000/callback',
      'https://example.com/oauth/callback',
      'https://staging.example.com/oauth/callback' // Adding new URI
    ], // ✅ TypeScript array - easy to modify!
    webhook_events: [
      'token.created',
      'token.revoked',
      'client.updated',
      'token.refreshed' // Adding new event
    ], // ✅ TypeScript array - type-safe additions!
    client_name: 'Updated Example App'
  }

  try {
    const updatedClient = await oauthClientService.updateClient(clientId, updates)
    if (updatedClient) {
      console.log('✅ Client updated:', {
        id: updatedClient.id,
        name: updatedClient.client_name,
        redirect_uris: updatedClient.redirect_uris, // Returns as parsed array
        webhook_events: updatedClient.webhook_events // Returns as parsed array
      })
    }
    return updatedClient
  } catch (error) {
    console.error('❌ Failed to update client:', error)
    throw error
  }
}

// Example: Type-safe JSONB field manipulation
function demonstrateTypeSafety() {
  console.log('🔍 Demonstrating type safety benefits...')

  // ✅ BEFORE: TypeScript provides full intellisense and type checking
  const clientData: OAuthClientPreInsert = {
    client_name: 'Type Safe App',
    redirect_uris: [
      'http://localhost:3000/callback'
      // TypeScript will catch missing commas, wrong types, etc.
    ],
    grant_types: [
      'authorization_code',
      'refresh_token'
      // TypeScript knows these should be strings
    ],
    scope: 'read write',
    webhook_events: [
      'token.created',
      'token.revoked'
      // TypeScript provides autocomplete for known events
    ]
  }

  // ✅ Type checking prevents common mistakes:
  // clientData.redirect_uris = 'string' // ❌ TypeScript error!
  // clientData.grant_types = ['invalid'] // ✅ Still string[], but we could add enum validation
  // clientData.webhook_events = null // ✅ Allowed
  // clientData.webhook_events = undefined // ✅ Allowed

  console.log('✅ Type safety ensures correct data structure')
  return clientData
}

// Example: Comparison of old vs new approach
function comparisonExample() {
  console.log('📊 Comparing old vs new approach...')

  // ❌ OLD WAY: Manual JSON.stringify, error-prone, no type safety
  const oldWay = {
    client_name: 'Old Way App',
    redirect_uris: JSON.stringify(['http://localhost:3000/callback']), // Manual stringify
    grant_types: JSON.stringify(['authorization_code']), // Manual stringify
    scope: 'read',
    webhook_events: JSON.stringify(['token.created']) // Manual stringify
  }

  // ✅ NEW WAY: Type-safe arrays, automatic conversion, better maintainability
  const newWay: OAuthClientPreInsert = {
    client_name: 'New Way App',
    redirect_uris: ['http://localhost:3000/callback'], // ✅ Type-safe array
    grant_types: ['authorization_code'], // ✅ Type-safe array
    scope: 'read',
    webhook_events: ['token.created'] // ✅ Type-safe array
  }

  console.log('❌ Old way requires manual JSON.stringify:', oldWay)
  console.log('✅ New way uses proper TypeScript types:', newWay)

  return { oldWay, newWay }
}

// Main demonstration function
export async function demonstrateJsonbTypes() {
  console.log('🚀 JSONB Types Usage Demonstration\n')

  try {
    // Show type safety benefits
    demonstrateTypeSafety()
    console.log('')

    // Show comparison
    comparisonExample()
    console.log('')

    // Create a client
    const client = await createExampleClient()
    console.log('')

    // Update the client
    if (client) {
      await updateExampleClient(client.id)
    }

    console.log('\n✅ Demonstration completed successfully!')
  } catch (error) {
    console.error('\n❌ Demonstration failed:', error)
  }
}

// Export types for reference
export type {
  OAuthClientPreInsert,
  OAuthClientPreUpdate
}
