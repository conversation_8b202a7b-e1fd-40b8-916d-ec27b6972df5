#!/usr/bin/env node

import { db } from '@db/database'

interface JsonTestRow {
  id: number
  test_json: any
}

async function createTestTable() {
  console.log('🔧 Creating test table...')
  
  // Drop table if exists
  await db.schema.dropTable('json_test').ifExists().execute()
  
  // Create test table
  await db.schema
    .createTable('json_test')
    .addColumn('id', 'serial', (col) => col.primaryKey())
    .addColumn('test_json', 'jsonb')
    .execute()
    
  console.log('✅ Test table created')
}

async function testJsonType(label: string, value: any, useStringify: boolean = false) {
  console.log(`\n📝 Testing ${label}:`)
  console.log(`   Input value:`, value)
  console.log(`   Input type:`, typeof value)
  console.log(`   Using JSON.stringify:`, useStringify)
  
  try {
    // Insert the value
    const insertValue = useStringify ? JSON.stringify(value) : value
    console.log(`   Insert value:`, insertValue)
    console.log(`   Insert type:`, typeof insertValue)
    
    const result = await db
      .insertInto('json_test')
      .values({ test_json: insertValue })
      .returning(['id', 'test_json'])
      .executeTakeFirst()
    
    if (result) {
      console.log(`   ✅ Insert successful`)
      console.log(`   Returned value:`, result.test_json)
      console.log(`   Returned type:`, typeof result.test_json)
      
      // Now fetch it back
      const fetched = await db
        .selectFrom('json_test')
        .selectAll()
        .where('id', '=', result.id)
        .executeTakeFirst()
      
      if (fetched) {
        console.log(`   📤 Fetched value:`, fetched.test_json)
        console.log(`   📤 Fetched type:`, typeof fetched.test_json)
        
        // Check if we need to parse
        if (typeof fetched.test_json === 'string') {
          try {
            const parsed = JSON.parse(fetched.test_json)
            console.log(`   🔄 Parsed value:`, parsed)
            console.log(`   🔄 Parsed type:`, typeof parsed)
          } catch (e) {
            console.log(`   ❌ Parse failed:`, e.message)
          }
        }
        
        // Check equality
        const isEqual = JSON.stringify(value) === JSON.stringify(fetched.test_json)
        console.log(`   🎯 Values equal:`, isEqual)
      }
    }
  } catch (error) {
    console.log(`   ❌ Error:`, error.message)
  }
}

async function runTests() {
  console.log('🧪 Starting JSONB Type Tests\n')
  
  await createTestTable()
  
  // Test different JSON types
  const testCases = [
    // Objects
    { label: 'Simple Object', value: { name: 'John', age: 30 } },
    { label: 'Nested Object', value: { user: { name: 'John', settings: { theme: 'dark' } } } },
    
    // Arrays
    { label: 'String Array', value: ['apple', 'banana', 'cherry'] },
    { label: 'Number Array', value: [1, 2, 3, 4, 5] },
    { label: 'Mixed Array', value: ['text', 123, true, null, { key: 'value' }] },
    
    // Primitives
    { label: 'String', value: 'Hello World' },
    { label: 'Number', value: 42 },
    { label: 'Boolean True', value: true },
    { label: 'Boolean False', value: false },
    { label: 'Null', value: null },
    
    // Edge cases
    { label: 'Empty Object', value: {} },
    { label: 'Empty Array', value: [] },
    { label: 'JSON String', value: '{"already": "json"}' },
  ]
  
  // Test each case without JSON.stringify
  console.log('\n🔍 Testing WITHOUT JSON.stringify():')
  for (const testCase of testCases) {
    await testJsonType(testCase.label, testCase.value, false)
  }
  
  // Test each case with JSON.stringify
  console.log('\n🔍 Testing WITH JSON.stringify():')
  for (const testCase of testCases) {
    await testJsonType(`${testCase.label} (stringified)`, testCase.value, true)
  }
  
  // Summary
  console.log('\n📊 SUMMARY:')
  console.log('   - PostgreSQL JSONB columns automatically handle JSON conversion')
  console.log('   - Objects and arrays can be inserted directly without JSON.stringify()')
  console.log('   - Values are returned as parsed JavaScript objects/arrays')
  console.log('   - Primitive values (strings, numbers, booleans) work as expected')
  console.log('   - Using JSON.stringify() creates double-encoded JSON strings')
}

async function cleanup() {
  console.log('\n🧹 Cleaning up...')
  await db.schema.dropTable('json_test').ifExists().execute()
  console.log('✅ Test table dropped')
  
  // Close database connection
  await db.destroy()
  console.log('✅ Database connection closed')
}

async function main() {
  try {
    await runTests()
  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    await cleanup()
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error)
}

export { main as testJsonb }
