import { oauthClientService } from '@services/oauthClientService'
import { cleanupTestOAuthClients } from '@test/setup'
import { afterEach, beforeEach, describe, expect, it } from 'vitest'

describe('OAuthClientService', () => {
  afterEach(async () => {
    // Clean up test data
    await cleanupTestOAuthClients()
  })

  describe('Client Creation', () => {
    it('should create a new OAuth client', async () => {
      const clientData = {
        client_name: 'Test Client',
        redirect_uris: ['http://localhost:3000/callback'],
        grant_types: ['authorization_code', 'refresh_token'],
        scope: 'read write',
      }

      const client = await oauthClientService.createClient(clientData)

      expect(client).toHaveProperty('id')
      expect(client).toHaveProperty('client_id')
      expect(client).toHaveProperty('client_secret')
      expect(client.client_name).toBe('Test Client')
      expect(client.scope).toBe('read write')
      expect(client.is_active).toBe(1) // MySQL uses 1 for true
      expect(typeof client.client_id).toBe('string')
      expect(typeof client.client_secret).toBe('string')
      expect(client.client_id.length).toBeGreaterThan(0)
      expect(client.client_secret.length).toBeGreaterThan(0)
    })

    it('should create client with webhook configuration', async () => {
      const clientData = {
        client_name: 'Test Webhook Client',
        redirect_uris: ['http://localhost:3000/callback'],
        grant_types: ['authorization_code'],
        scope: 'read',
        webhook_url: 'http://localhost:3000/webhook',
        webhook_secret: 'webhook-secret',
        webhook_events: ['token.revoked', 'token.refreshed'],
      }

      const client = await oauthClientService.createClient(clientData)

      expect(client.webhook_url).toBe('http://localhost:3000/webhook')
      expect(client.webhook_secret).toBe('webhook-secret')
      expect(client.webhook_events).toEqual([
        'token.revoked',
        'token.refreshed',
      ])
    })
  })

  describe('Client Retrieval', () => {
    let testClient: any

    beforeEach(async () => {
      testClient = await oauthClientService.createClient({
        client_name: 'Test Client',
        redirect_uris: ['http://localhost:3000/callback'],
        grant_types: ['authorization_code', 'refresh_token'],
        scope: 'read write',
      })
    })

    it('should get client by ID', async () => {
      const client = await oauthClientService.getClientById(testClient.id)

      expect(client).not.toBeUndefined()
      expect(client?.id).toBe(testClient.id)
      expect(client?.client_name).toBe('Test Client')
    })

    it('should get client by client_id', async () => {
      const client = await oauthClientService.getClientByClientId(
        testClient.client_id
      )

      expect(client).not.toBeUndefined()
      expect(client?.client_id).toBe(testClient.client_id)
      expect(client?.client_name).toBe('Test Client')
    })

    it('should return undefined for non-existent client', async () => {
      const client = await oauthClientService.getClientById(99999)
      expect(client).toBeUndefined()
    })

    it('should return undefined for non-existent client_id', async () => {
      const client = await oauthClientService.getClientByClientId(
        'non-existent-client-id'
      )
      expect(client).toBeUndefined()
    })

    it('should get all clients', async () => {
      const clients = await oauthClientService.getAllClients()

      expect(Array.isArray(clients)).toBe(true)
      expect(clients.length).toBeGreaterThan(0)

      const testClientInList = clients.find((c) => c.id === testClient.id)
      expect(testClientInList).not.toBeUndefined()
    })
  })

  describe('Client Validation', () => {
    let testClient: any

    beforeEach(async () => {
      testClient = await oauthClientService.createClient({
        client_name: 'Test Client',
        redirect_uris: [
          'http://localhost:3000/callback',
          'http://localhost:3001/callback',
        ],
        grant_types: ['authorization_code', 'refresh_token'],
        scope: 'read write',
      })
    })

    it('should validate client credentials', async () => {
      const isValid = await oauthClientService.validateClient(
        testClient.client_id,
        testClient.client_secret
      )

      expect(isValid).toBeTruthy()
    })

    it('should reject invalid client credentials', async () => {
      const isValid = await oauthClientService.validateClient(
        testClient.client_id,
        'wrong-secret'
      )

      expect(isValid).toBeFalsy()
    })

    it('should reject non-existent client', async () => {
      const isValid = await oauthClientService.validateClient(
        'non-existent-client',
        'any-secret'
      )

      expect(isValid).toBeFalsy()
    })

    it('should validate redirect URI', async () => {
      const isValid = await oauthClientService.validateRedirectUri(
        testClient.client_id,
        'http://localhost:3000/callback'
      )

      expect(isValid).toBe(true)
    })

    it('should reject invalid redirect URI', async () => {
      const isValid = await oauthClientService.validateRedirectUri(
        testClient.client_id,
        'http://malicious.com/callback'
      )

      expect(isValid).toBe(false)
    })

    it('should validate grant type', async () => {
      const isValid = await oauthClientService.validateGrantType(
        testClient.client_id,
        'authorization_code'
      )

      expect(isValid).toBe(true)
    })

    it('should reject invalid grant type', async () => {
      const isValid = await oauthClientService.validateGrantType(
        testClient.client_id,
        'client_credentials'
      )

      expect(isValid).toBe(false)
    })
  })

  describe('Client Updates', () => {
    let testClient: any

    beforeEach(async () => {
      testClient = await oauthClientService.createClient({
        client_name: 'Test Client',
        redirect_uris: ['http://localhost:3000/callback'],
        grant_types: ['authorization_code'],
        scope: 'read',
      })
    })

    it('should update client information', async () => {
      const updates = {
        client_name: 'Updated Test Client',
        scope: 'read write',
        redirect_uris: [
          'http://localhost:3000/callback',
          'http://localhost:3001/callback',
        ],
      }

      const updatedClient = await oauthClientService.updateClient(
        testClient.id,
        updates
      )

      expect(updatedClient).not.toBeUndefined()
      expect(updatedClient?.client_name).toBe('Updated Test Client')
      expect(updatedClient?.scope).toBe('read write')
      expect(updatedClient?.redirect_uris).toEqual([
        'http://localhost:3000/callback',
        'http://localhost:3001/callback',
      ])
    })

    it('should deactivate client', async () => {
      const updatedClient = await oauthClientService.updateClient(
        testClient.id,
        {
          is_active: false,
        }
      )

      expect(updatedClient?.is_active).toBe(0) // MySQL uses 0 for false
    })
  })

  describe('Client Deletion', () => {
    let testClient: any

    beforeEach(async () => {
      testClient = await oauthClientService.createClient({
        client_name: 'Test Client',
        redirect_uris: ['http://localhost:3000/callback'],
        grant_types: ['authorization_code'],
        scope: 'read',
      })
    })

    it('should delete client', async () => {
      const deleted = await oauthClientService.deleteClient(testClient.id)
      expect(deleted).toBe(true)

      // Client should no longer exist
      const client = await oauthClientService.getClientById(testClient.id)
      expect(client).toBeUndefined()
    })

    it('should return false when deleting non-existent client', async () => {
      const deleted = await oauthClientService.deleteClient(99999)
      expect(deleted).toBe(false)
    })
  })
})
