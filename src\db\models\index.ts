import { EmployeesTable } from './employees'
import { OAuthClientsTable } from './oauth_clients'
import { OAuthTokensTable } from './oauth_tokens'
import { PasswordResetCodesTable } from './password_reset_codes'

export interface Database {
  'auth.employees': EmployeesTable
  'auth.oauth_clients': OAuthClientsTable
  'auth.oauth_tokens': OAuthTokensTable
  'auth.password_reset_codes': PasswordResetCodesTable
}

// You should not use the table schema interfaces directly. Instead, you should
// use the `Selectable`, `Insertable` and `Updateable` wrappers. These wrappers
// make sure that the correct types are used in each operation.
