import crypto from 'crypto'
import { db } from '../database'
import { NewOAuthClient } from '../models/oauth_clients'

async function seedOAuthClients() {
  console.log('🌱 Seeding OAuth clients...')

  try {
    // Check if clients already exist
    const existingClients = await db
      .selectFrom('auth.oauth_clients')
      .select(['client_id'])
      .execute()

    const existingClientIds = existingClients.map((c) => c.client_id)

    const clients: NewOAuthClient[] = [
      {
        client_id: 'varpro-client',
        client_secret: crypto
          .createHash('sha256')
          .update('varpro-client-secret')
          .digest('hex'),
        client_name: 'VarPro Standard Client',
        redirect_uris: [
          'http://localhost:3230/callback',
          'http://localhost:3230/dashboard.html',
        ],
        grant_types: ['password', 'authorization_code', 'refresh_token'],
        scope: 'read write',
        is_active: true,
        webhook_url: null,
        webhook_secret: null,
        webhook_events: null,
      },
      {
        client_id: 'varpro-admin',
        client_secret: crypto
          .createHash('sha256')
          .update('varpro-admin-secret')
          .digest('hex'),
        client_name: 'VarPro Admin Panel',
        redirect_uris: [
          'http://localhost:3230/admin',
          'http://localhost:3230/admin/callback',
        ],
        grant_types: ['password', 'authorization_code', 'refresh_token'],
        scope: 'read write admin',
        is_active: true,
        webhook_url: null,
        webhook_secret: null,
        webhook_events: null,
      },
    ]

    for (const client of clients) {
      if (!existingClientIds.includes(client.client_id)) {
        await db.insertInto('auth.oauth_clients').values(client).execute()

        console.log(
          `✅ Created OAuth client: ${client.client_name} (${client.client_id})`
        )
      } else {
        console.log(`⏭️  OAuth client already exists: ${client.client_id}`)
      }
    }

    console.log('🎉 OAuth client seeding completed!')
  } catch (error) {
    console.error('❌ Error seeding OAuth clients:', error)
    throw error
  }
}

// Run the seeding function if this script is executed directly
if (require.main === module) {
  seedOAuthClients()
    .then(() => {
      console.log('✨ Seeding completed successfully')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Seeding failed:', error)
      process.exit(1)
    })
}

export { seedOAuthClients }
