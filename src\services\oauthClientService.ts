import * as crypto from 'crypto'
import { v4 as uuidv4 } from 'uuid'
import { db } from '../db/database'
import { OAuthClient, OAuthClientUpdate } from '../db/models/oauth_clients'

export class OAuthClientService {
  async getAllClients(): Promise<OAuthClient[]> {
    const clients = await db
      .selectFrom('auth.oauth_clients')
      .selectAll()
      .where('is_active', '=', true)
      .orderBy('created_at', 'desc')
      .execute()

    return clients.map((client) => ({
      ...client,
      redirect_uris: client.redirect_uris,
      grant_types: client.grant_types,
      webhook_events: client.webhook_events ? client.webhook_events : null,
    }))
  }

  async getClientById(id: number): Promise<OAuthClient | undefined> {
    const client = await db
      .selectFrom('auth.oauth_clients')
      .selectAll()
      .where('id', '=', id)
      .where('is_active', '=', true)
      .executeTakeFirst()

    if (!client) return undefined

    return {
      ...client,
      redirect_uris: client.redirect_uris,
      grant_types: client.grant_types,
      webhook_events: client.webhook_events ? client.webhook_events : null,
    }
  }

  async getClientByClientId(
    clientId: string
  ): Promise<OAuthClient | undefined> {
    const client = await db
      .selectFrom('auth.oauth_clients')
      .selectAll()
      .where('client_id', '=', clientId)
      .where('is_active', '=', true)
      .executeTakeFirst()

    if (!client) return undefined

    return {
      ...client,
      redirect_uris: client.redirect_uris,
      grant_types: client.grant_types,
      webhook_events: client.webhook_events ? client.webhook_events : null,
    }
  }

  async createClient(clientData: {
    client_name: string
    redirect_uris: string[]
    grant_types: string[]
    scope: string
    is_active?: boolean
    webhook_url?: string | null
    webhook_secret?: string | null
    webhook_events?: string[] | null
  }): Promise<OAuthClient> {
    const clientId = uuidv4()
    const clientSecret = crypto.randomBytes(32).toString('hex')

    let webhookSecret = clientData.webhook_secret
    if (clientData.webhook_url && !webhookSecret) {
      webhookSecret = crypto.randomBytes(32).toString('hex')
    }

    // Insert without RETURNING (MySQL doesn't support it)
    await db
      .insertInto('auth.oauth_clients')
      .values({
        client_id: clientId,
        client_secret: clientSecret,
        client_name: clientData.client_name,
        redirect_uris: clientData.redirect_uris,
        grant_types: clientData.grant_types,
        scope: clientData.scope,
        is_active: clientData.is_active ?? true,
        webhook_url: clientData.webhook_url || null,
        webhook_secret: webhookSecret || null,
        webhook_events: clientData.webhook_events
          ? clientData.webhook_events
          : null,
      })
      .execute()

    // Fetch the created client
    const result = await this.getClientByClientId(clientId)
    if (!result) {
      throw new Error('Failed to create OAuth client')
    }

    return result
  }

  async updateClient(
    id: number,
    updates: OAuthClientUpdate
  ): Promise<OAuthClient | undefined> {
    const updateData: OAuthClientUpdate = {
      ...updates,
      updated_at: new Date().toISOString(),
    }

    if (updates.redirect_uris) {
      updateData.redirect_uris = updates.redirect_uris
    }

    if (updates.grant_types) {
      updateData.grant_types = updates.grant_types
    }

    if (updates.webhook_events) {
      updateData.webhook_events = updates.webhook_events
    }

    const result = await db
      .updateTable('auth.oauth_clients')
      .set(updateData)
      .where('id', '=', id)
      .execute()

    if (result.length === 0 || Number(result[0].numUpdatedRows) === 0) {
      return undefined
    }

    return await this.getClientById(id)
  }

  async deleteClient(id: number): Promise<boolean> {
    const result = await db
      .updateTable('auth.oauth_clients')
      .set({
        is_active: false,
        updated_at: new Date().toISOString(),
      })
      .where('id', '=', id)
      .execute()

    return result.length > 0 && Number(result[0].numUpdatedRows) > 0
  }

  async validateClient(
    clientId: string,
    clientSecret?: string
  ): Promise<OAuthClient | null> {
    const client = await this.getClientByClientId(clientId)

    if (!client) {
      return null
    }

    // For public clients (like SPAs), client_secret might not be required
    if (clientSecret && client.client_secret !== clientSecret) {
      return null
    }

    return client
  }

  async validateRedirectUri(
    clientId: string,
    redirectUri: string
  ): Promise<boolean> {
    const client = await this.getClientByClientId(clientId)

    if (!client) {
      return false
    }

    return client.redirect_uris.includes(redirectUri)
  }

  async validateGrantType(
    clientId: string,
    grantType: string
  ): Promise<boolean> {
    const client = await this.getClientByClientId(clientId)

    if (!client) {
      return false
    }

    return client.grant_types.includes(grantType)
  }
}

export const oauthClientService = new OAuthClientService()
