import { db } from '@db/database'
import { OAuthClient } from '@models/oauth_clients'
import * as crypto from 'crypto'
import { v4 as uuidv4 } from 'uuid'

// Service layer interface that accepts arrays for JSONB fields
export interface OAuthClientServiceUpdate {
  client_name?: string
  redirect_uris?: string[]
  grant_types?: string[]
  scope?: string
  is_active?: boolean
  webhook_url?: string | null
  webhook_secret?: string | null
  webhook_events?: string[] | null
}

export class OAuthClientService {
  // Helper method to parse JSONB columns from database results
  private parseClientJsonFields(client: any): OAuthClient {
    return {
      ...client,
      redirect_uris:
        typeof client.redirect_uris === 'string'
          ? JSON.parse(client.redirect_uris)
          : client.redirect_uris,
      grant_types:
        typeof client.grant_types === 'string'
          ? JSON.parse(client.grant_types)
          : client.grant_types,
      webhook_events: client.webhook_events
        ? typeof client.webhook_events === 'string'
          ? JSON.parse(client.webhook_events)
          : client.webhook_events
        : null,
    }
  }
  async getAllClients(): Promise<OAuthClient[]> {
    const clients = await db
      .selectFrom('auth.oauth_clients')
      .selectAll()
      .where('is_active', '=', true)
      .orderBy('created_at', 'desc')
      .execute()

    return clients.map((client) => this.parseClientJsonFields(client))
  }

  async getClientById(
    id: number,
    includeInactive = false
  ): Promise<OAuthClient | undefined> {
    let query = db
      .selectFrom('auth.oauth_clients')
      .selectAll()
      .where('id', '=', id)

    if (!includeInactive) {
      query = query.where('is_active', '=', true)
    }

    const client = await query.executeTakeFirst()

    if (!client) return undefined

    return this.parseClientJsonFields(client)
  }

  async getClientByClientId(
    clientId: string
  ): Promise<OAuthClient | undefined> {
    const client = await db
      .selectFrom('auth.oauth_clients')
      .selectAll()
      .where('client_id', '=', clientId)
      .where('is_active', '=', true)
      .executeTakeFirst()

    if (!client) return undefined

    return this.parseClientJsonFields(client)
  }

  async createClient(clientData: {
    client_name: string
    redirect_uris: string[]
    grant_types: string[]
    scope: string
    is_active?: boolean
    webhook_url?: string | null
    webhook_secret?: string | null
    webhook_events?: string[] | null
  }): Promise<OAuthClient> {
    const clientId = uuidv4()
    const clientSecret = crypto.randomBytes(32).toString('hex')

    let webhookSecret = clientData.webhook_secret
    if (clientData.webhook_url && !webhookSecret) {
      webhookSecret = crypto.randomBytes(32).toString('hex')
    }

    // Insert with JSON.stringify for JSONB columns
    await db
      .insertInto('auth.oauth_clients')
      .values({
        client_id: clientId,
        client_secret: clientSecret,
        client_name: clientData.client_name,
        redirect_uris: JSON.stringify(clientData.redirect_uris),
        grant_types: JSON.stringify(clientData.grant_types),
        scope: clientData.scope,
        is_active: clientData.is_active ?? true,
        webhook_url: clientData.webhook_url || null,
        webhook_secret: webhookSecret || null,
        webhook_events: clientData.webhook_events
          ? JSON.stringify(clientData.webhook_events)
          : null,
      })
      .execute()

    // Fetch the created client
    const result = await this.getClientByClientId(clientId)
    if (!result) {
      throw new Error('Failed to create OAuth client')
    }

    return result
  }

  async updateClient(
    id: number,
    updates: OAuthClientServiceUpdate
  ): Promise<OAuthClient | undefined> {
    const updateData: Record<string, any> = {
      ...updates,
      updated_at: new Date().toISOString(),
    }

    // Handle JSONB columns - stringify arrays for database storage
    if (updates.redirect_uris) {
      updateData.redirect_uris = JSON.stringify(updates.redirect_uris)
    }

    if (updates.grant_types) {
      updateData.grant_types = JSON.stringify(updates.grant_types)
    }

    if (updates.webhook_events) {
      updateData.webhook_events = JSON.stringify(updates.webhook_events)
    }

    await db
      .updateTable('auth.oauth_clients')
      .set(updateData)
      .where('id', '=', id)
      .execute()

    // Return the updated client (include inactive clients to show deactivated state)
    return await this.getClientById(id, true)
  }

  async deleteClient(id: number): Promise<boolean> {
    // First check if the client exists
    const existingClient = await this.getClientById(id, true)
    if (!existingClient) {
      return false // Client doesn't exist
    }

    await db
      .updateTable('auth.oauth_clients')
      .set({
        is_active: false,
        updated_at: new Date().toISOString(),
      })
      .where('id', '=', id)
      .execute()

    return true // Successfully deactivated
  }

  async validateClient(
    clientId: string,
    clientSecret?: string
  ): Promise<OAuthClient | null> {
    const client = await this.getClientByClientId(clientId)

    if (!client) {
      return null
    }

    // For public clients (like SPAs), client_secret might not be required
    if (clientSecret && client.client_secret !== clientSecret) {
      return null
    }

    return client
  }

  async validateRedirectUri(
    clientId: string,
    redirectUri: string
  ): Promise<boolean> {
    const client = await this.getClientByClientId(clientId)

    if (!client) {
      return false
    }

    return client.redirect_uris.includes(redirectUri)
  }

  async validateGrantType(
    clientId: string,
    grantType: string
  ): Promise<boolean> {
    const client = await this.getClientByClientId(clientId)

    if (!client) {
      return false
    }

    return client.grant_types.includes(grantType)
  }
}

export const oauthClientService = new OAuthClientService()
