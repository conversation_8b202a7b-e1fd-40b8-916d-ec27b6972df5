import { db } from '@db/database'
import { 
  OAuthClient, 
  OAuthClientPreInsert, 
  OAuthClientPreUpdate 
} from '@models/oauth_clients'
import { 
  prepareOAuthClientForInsert, 
  prepareOAuthClientForUpdate 
} from '@utils/jsonbHelpers'
import * as crypto from 'crypto'
import { v4 as uuidv4 } from 'uuid'

export class OAuthClientService {
  async getAllClients(): Promise<OAuthClient[]> {
    const clients = await db
      .selectFrom('auth.oauth_clients')
      .selectAll()
      .where('is_active', '=', true)
      .orderBy('created_at', 'desc')
      .execute()

    return clients
  }

  async getClientById(
    id: number,
    includeInactive = false
  ): Promise<OAuthClient | undefined> {
    let query = db
      .selectFrom('auth.oauth_clients')
      .selectAll()
      .where('id', '=', id)

    if (!includeInactive) {
      query = query.where('is_active', '=', true)
    }

    const client = await query.executeTakeFirst()

    if (!client) return undefined

    return client
  }

  async getClientByClientId(
    clientId: string
  ): Promise<OAuthClient | undefined> {
    const client = await db
      .selectFrom('auth.oauth_clients')
      .selectAll()
      .where('client_id', '=', clientId)
      .where('is_active', '=', true)
      .executeTakeFirst()

    if (!client) return undefined

    return client
  }

  async createClient(clientData: OAuthClientPreInsert): Promise<OAuthClient> {
    const clientId = uuidv4()
    const clientSecret = crypto.randomBytes(32).toString('hex')

    let webhookSecret = clientData.webhook_secret
    if (clientData.webhook_url && !webhookSecret) {
      webhookSecret = crypto.randomBytes(32).toString('hex')
    }

    // Prepare data for database insertion (stringify JSONB fields)
    const dbData = prepareOAuthClientForInsert({
      ...clientData,
      client_id: clientId,
      client_secret: clientSecret,
      webhook_secret: webhookSecret || null,
      is_active: clientData.is_active ?? true,
    })

    await db
      .insertInto('auth.oauth_clients')
      .values(dbData)
      .execute()

    // Fetch the created client
    const result = await this.getClientByClientId(clientId)
    if (!result) {
      throw new Error('Failed to create OAuth client')
    }

    return result
  }

  async updateClient(
    id: number,
    updates: OAuthClientPreUpdate
  ): Promise<OAuthClient | undefined> {
    // Prepare data for database update (stringify JSONB fields)
    const updateData = prepareOAuthClientForUpdate({
      ...updates,
      updated_at: new Date().toISOString(),
    })

    await db
      .updateTable('auth.oauth_clients')
      .set(updateData)
      .where('id', '=', id)
      .execute()

    // Return the updated client (include inactive clients to show deactivated state)
    return await this.getClientById(id, true)
  }

  async deleteClient(id: number): Promise<boolean> {
    // First check if the client exists
    const existingClient = await this.getClientById(id, true)
    if (!existingClient) {
      return false // Client doesn't exist
    }

    await db
      .updateTable('auth.oauth_clients')
      .set({
        is_active: false,
        updated_at: new Date().toISOString(),
      })
      .where('id', '=', id)
      .execute()

    return true // Successfully deactivated
  }

  async validateClient(
    clientId: string,
    clientSecret?: string
  ): Promise<OAuthClient | null> {
    const client = await this.getClientByClientId(clientId)

    if (!client) {
      return null
    }

    // For public clients (like SPAs), client_secret might not be required
    if (clientSecret && client.client_secret !== clientSecret) {
      return null
    }

    return client
  }

  async validateRedirectUri(
    clientId: string,
    redirectUri: string
  ): Promise<boolean> {
    const client = await this.getClientByClientId(clientId)

    if (!client) {
      return false
    }

    return client.redirect_uris.includes(redirectUri)
  }

  async validateGrantType(
    clientId: string,
    grantType: string
  ): Promise<boolean> {
    const client = await this.getClientByClientId(clientId)

    if (!client) {
      return false
    }

    return client.grant_types.includes(grantType)
  }
}

export const oauthClientService = new OAuthClientService()
